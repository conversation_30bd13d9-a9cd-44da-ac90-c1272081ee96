<?php

namespace App\Repository;

use App\Entity\User;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;
use Symfony\Bridge\Doctrine\Security\User\UserLoaderInterface;
use App\Service\LdapService;

/**
 * @extends ServiceEntityRepository<User>
 */
class UserRepository extends ServiceEntityRepository implements UserLoaderInterface
{
    private $ldapService;

    public function __construct(ManagerRegistry $registry, LdapService $ldapService)
    {
        parent::__construct($registry, User::class);
        $this->ldapService = $ldapService;
    }



    public function loadUserByIdentifier(string $usernameOrEmail): ?User
    {
        $entityManager = $this->getEntityManager();
        $username = explode("\\", $_SERVER['REMOTE_USER'])[1];
        if($username == "") {
            $username ='acrapis';
        }
        if($username == "eerdmann") {
            $username ='acrapis';
        }
        // if($username == "acrapis") {
        //     // $username ='acrapis';
        //     // $username ='jfcribier';
        //     $username ='tlelong';

        // }
        if($username == "SCMXIBO") {
                // $username ='acrapis';
                // $username ='jfcribier';
            $username ='eerdmann';
        }
        // if($username == "srochdi") {
        //     // $username ='lmartineau';
        //     // $username ='jfcribier';
        //     // $username ='tlelong';
        //         $username ='acrapis';

        // }
        // $allusers = $this->ldapService->getAllUsersInfo();
        // dd($allusers);
        // foreach ($allusers as $ldapUserInfo) {

        //     $secteurRepository = $entityManager->getRepository(Service::class);
        //     $secteur = $secteurRepository->findOneBy(['name' => $ldapUserInfo['department']]);

        //     if (!$secteur && isset($ldapUserInfo['department'])) {
        //         $secteur = new Service();
        //         $secteur->setName($ldapUserInfo['department']);
        //         $entityManager->persist($secteur);
        //         $entityManager->flush();
        //     }

        //     $existingUser = $this->findOneBy(['username' => $ldapUserInfo['username']]);
        //     $nomComplet = preg_match('/CN=([^,]+)/', $ldapUserInfo['distinguishedName'], $matches);
        //     if ($nomComplet) {
        //         $test = explode(' ', $matches[1]);
        //         if (count($test) < 2) {
        //             continue;
        //         }else{

        //             list($nom, $prenom) = explode(' ', $matches[1]);
        //         }
        //     }
        //     $department = $this->normalizeDepartment($ldapUserInfo['department'] ?? 'DEFAULT');
        //     $roles = $ldapUserInfo['isManager']
        //         ? ['ROLE_MANAGER_' . $department]
        //         : ['ROLE_USER_' . $department];
        //     $isInVpn = $this->ldapService->isUserInVpn($ldapUserInfo['username'], 'VPN_SSL', 'OU=FRSCM_Groupes,DC=scmlemans,DC=com');
        //     if ($existingUser) {
        //         // Mettre à jour l'utilisateur existant
        //         $existingUser->setEmail($ldapUserInfo['email'] ?? $existingUser->getEmail());
        //         $existingUser->setNom($nom ?? $existingUser->getNom());
        //         $existingUser->setPrenom($prenom ?? $existingUser->getPrenom());
        //         $existingUser->setRoles($roles);
        //         $existingUser->setSecteur($ldapUserInfo['department'] ?? $existingUser->getSecteur());
        //         $existingUser->setManager($ldapUserInfo['manager']['distinguishedName'] ?? $existingUser->getManager());
        //         $existingUser->setIsManager($ldapUserInfo['isManager'] ?? $existingUser->getIsManager());
        //         $existingUser->setTitre($ldapUserInfo['title'] ?? $existingUser->getTitre());
        //         $existingUser->setVpn($isInVpn);
        //         $existingUser->setMobile($ldapUserInfo['mobile'] ?? null);

        //         $entityManager->flush();

        //     } elseif ($ldapUserInfo['email'] != "<EMAIL>") {
        //         // Créer un nouvel utilisateur
        //         $newUser = new User();
        //         $newUser->setUsername($ldapUserInfo['username']);
        //         $newUser->setEmail($ldapUserInfo['email'] ?? null);
        //         $newUser->setNom($nom ?? null);
        //         $newUser->setPrenom($prenom ?? null);
        //         $newUser->setRoles($roles);
        //         $newUser->setSecteur($ldapUserInfo['department'] ?? 'null');
        //         $newUser->setManager($ldapUserInfo['manager']['distinguishedName'] ?? null);
        //         $newUser->setIsManager($ldapUserInfo['isManager'] ?? null);
        //         $newUser->setTitre($ldapUserInfo['title'] ?? null);
        //         $newUser->setVpn($isInVpn);
        //         $newUser->setMobile($ldapUserInfo['mobile'] ?? null);
        //         $entityManager->persist($newUser);
        //         $entityManager->flush();

        //     }
        // }

        $ldapUserInfo = $this->ldapService->getUserInfoByUsernameInscription($username);
        $isInVpn = $this->ldapService->isUserInVpn($username, 'VPN_SSL', 'OU=FRSCM_Groupes,DC=scmlemans,DC=com');



        $existingUser = $this->findOneBy(['username' => $ldapUserInfo['username']]);
        $nomComplet = preg_match('/CN=([^,]+)/', $ldapUserInfo['distinguishedName'], $matches);
        if ($nomComplet) {
            list($nom, $prenom) = explode(' ', $matches[1], 2);
        }
        $department = $this->normalizeDepartment($ldapUserInfo['department'] ?? 'DEFAULT');
        $readableDepartment = $this->getReadableDepartment($ldapUserInfo['department'] ?? null);
        $roles = $ldapUserInfo['isManager']
            ? ['ROLE_MANAGER_' . $department]
            : ['ROLE_USER_' . $department];

        if ($existingUser) {
            // Mettre à jour l'utilisateur existant

            $existingUser->setEmail($ldapUserInfo['email'] ?? $existingUser->getEmail());
            $existingUser->setNom($nom ?? $existingUser->getNom());
            $existingUser->setPrenom($prenom ?? $existingUser->getPrenom());
            $existingUser->setRoles($roles);
            $existingUser->setSecteur($readableDepartment);
            $existingUser->setManager($ldapUserInfo['manager']['distinguishedName'] ?? $existingUser->getManager());
            $existingUser->setIsManager($ldapUserInfo['isManager'] ?? $existingUser->getIsManager());
            $existingUser->setTitre($ldapUserInfo['title'] ?? $existingUser->getTitre());
            $existingUser->setVpn($isInVpn);
            $existingUser->setMobile($ldapUserInfo['mobile'] ?? $existingUser->getMobile());
            $entityManager->flush();
            return $existingUser;
        } else {
            // Créer un nouvel utilisateur
            $newUser = new User();
            $newUser->setUsername($ldapUserInfo['username']);
            $newUser->setEmail($ldapUserInfo['email'] ?? null);
            $newUser->setNom($nom ?? null);
            $newUser->setPrenom($prenom ?? null);
            $newUser->setRoles($roles);
            $newUser->setSecteur($readableDepartment);
            $newUser->setManager($ldapUserInfo['manager']['distinguishedName'] ?? null);
            $newUser->setIsManager($ldapUserInfo['isManager'] ?? null);
            $newUser->setTitre($ldapUserInfo['title'] ?? null);
            $newUser->setVpn($isInVpn);
            $newUser->setMobile($ldapUserInfo['mobile'] ?? null);
            $entityManager->persist($newUser);
            $entityManager->flush();
            return $newUser;
        }
    }

    private function normalizeDepartment(?string $department): string
    {
        if (empty($department)) {
            return 'DEFAULT';
        }
        // Remplace les espaces ou caractères spéciaux par des underscores pour les rôles
        $normalized = preg_replace('/[^A-Z0-9]/', '_', strtoupper($department));

        return $normalized;
    }

    /**
     * Retourne le nom lisible du département (avec accents et espaces)
     */
    private function getReadableDepartment(?string $department): string
    {
        if (empty($department)) {
            return 'Non défini';
        }

        // Nettoie le département mais conserve les accents et espaces
        return trim($department);
    }



    /**
     * Trouve les utilisateurs dont le manager contient le nom et prénom spécifiés
     */
    public function findUsersWithManager(string $nom, string $prenom): array
    {
        $qb = $this->createQueryBuilder('u');
        $qb->where('u.manager LIKE :manager')
           ->setParameter('manager', '%' . $nom . ' ' . $prenom . '%')
           ->orderBy('u.nom', 'ASC');

        return $qb->getQuery()->getResult();
    }


    /**
     * Récupère tous les utilisateurs qui ont un numéro de téléphone
     */
    public function findAllWithMobile(): array
    {
        return $this->createQueryBuilder('u')
            ->where('u.mobile IS NOT NULL')
            ->orderBy('u.nom', 'ASC')
            ->addOrderBy('u.prenom', 'ASC')
            ->getQuery()
            ->getResult();
    }

    /**
     * Recherche des utilisateurs par nom, prénom ou email
     */
    public function searchByNameOrEmail(string $query): array
    {
        return $this->createQueryBuilder('u')
            ->where('u.nom LIKE :query')
            ->orWhere('u.prenom LIKE :query')
            ->orWhere('u.email LIKE :query')
            ->setParameter('query', '%' . $query . '%')
            ->orderBy('u.nom', 'ASC')
            ->addOrderBy('u.prenom', 'ASC')
            ->getQuery()
            ->getResult();
    }

    /**
     * Trouve les utilisateurs actifs et OSI
     */
    public function findActifs(): array
    {
        return $this->createQueryBuilder('u')
            ->andWhere('u.actif = :actif')
            ->andWhere('u.userOsi = :userOsi')
            ->setParameter('actif', true)
            ->setParameter('userOsi', true)
            ->orderBy('u.nom', 'ASC')
            ->addOrderBy('u.prenom', 'ASC')
            ->getQuery()
            ->getResult();
    }

    /**
     * Trouve tous les utilisateurs (pour LDAP sync)
     */
    public function findAllUsers(): array
    {
        return $this->createQueryBuilder('u')
            ->orderBy('u.nom', 'ASC')
            ->addOrderBy('u.prenom', 'ASC')
            ->getQuery()
            ->getResult();
    }

    /**
     * Trouve les utilisateurs non-OSI (pour sélection)
     */
    public function findNonOsiUsers(): array
    {
        return $this->createQueryBuilder('u')
            ->where('u.userOsi = :userOsi OR u.userOsi IS NULL')
            ->setParameter('userOsi', false)
            ->orderBy('u.nom', 'ASC')
            ->addOrderBy('u.prenom', 'ASC')
            ->getQuery()
            ->getResult();
    }

    /**
     * Recherche d'utilisateurs par nom ou prénom
     */
    public function findByNomPrenom(string $search): array
    {
        return $this->createQueryBuilder('u')
            ->andWhere('u.nom LIKE :search OR u.prenom LIKE :search OR u.email LIKE :search')
            ->setParameter('search', '%' . $search . '%')
            ->andWhere('u.actif = :actif')
            ->setParameter('actif', true)
            ->orderBy('u.nom', 'ASC')
            ->addOrderBy('u.prenom', 'ASC')
            ->getQuery()
            ->getResult();
    }



    /**
     * Trouve les utilisateurs avec leurs missions
     */
    public function findWithMissions(): array
    {
        return $this->createQueryBuilder('u')
            ->leftJoin('u.missions', 'm')
            ->addSelect('m')
            ->andWhere('u.actif = :actif')
            ->setParameter('actif', true)
            ->orderBy('u.nom', 'ASC')
            ->addOrderBy('u.prenom', 'ASC')
            ->getQuery()
            ->getResult();
    }

    public function save(User $entity, bool $flush = false): void
    {
        $this->getEntityManager()->persist($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

    public function remove(User $entity, bool $flush = false): void
    {
        $this->getEntityManager()->remove($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

    public function initializeUsers(): void
    {
        $entityManager = $this->getEntityManager();
        $allUsers = $this->ldapService->getAllUsersInfo();

        foreach ($allUsers as $ldapUserInfo) {
            // $serviceRepository = $entityManager->getRepository(Service::class);
            // $service = $serviceRepository->findOneBy(['name' => $ldapUserInfo['department']]);

            // if (!$service && isset($ldapUserInfo['department'])) {
            //     $service = new Service();
            //     $service->setName($ldapUserInfo['department']);
            //     $entityManager->persist($service);
            //     $entityManager->flush();
            // }

            $existingUser = $this->findOneBy(['username' => $ldapUserInfo['username']]);
            $nomComplet = preg_match('/CN=([^,]+)/', $ldapUserInfo['distinguishedName'], $matches);
            if ($nomComplet) {
                $parts = explode(' ', $matches[1], 2);
                $nom = $parts[0] ?? '';
                $prenom = $parts[1] ?? '';
            }
            //     if (preg_match('/CN=([^,]+)/', $ldapUserInfo['distinguishedName'], $matches)) {
            //     $parts = explode(' ', $matches[1], 2);
            //     $nom = $parts[0] ?? null;
            //     $prenom = $parts[1] ?? null;
            // }

            $department = $this->normalizeDepartment($ldapUserInfo['department'] ?? 'DEFAULT');
            $readableDepartment = $this->getReadableDepartment($ldapUserInfo['department'] ?? null);
            $roles = $ldapUserInfo['isManager']
                ? ['ROLE_MANAGER_' . $department]
                : ['ROLE_USER_' . $department];

            if ($existingUser) {
                $existingUser->setEmail($ldapUserInfo['email'] ?? $existingUser->getEmail());
                $existingUser->setNom($nom ?? $existingUser->getNom());
                $existingUser->setPrenom($prenom ?? $existingUser->getPrenom());
                $existingUser->setRoles($roles);
                $existingUser->setSecteur($readableDepartment);
                $existingUser->setManager($ldapUserInfo['manager']['distinguishedName'] ?? $existingUser->getManager());
                $existingUser->setIsManager($ldapUserInfo['isManager'] ?? $existingUser->getIsManager());
                $existingUser->setTitre($ldapUserInfo['title'] ?? $existingUser->getTitre());
                if ($existingUser->getHoraireHebdo() === null) {
                    $existingUser->setHoraireHebdo(35.0);
                }
                $entityManager->flush();
            } elseif ($ldapUserInfo['email'] !== "<EMAIL>") {
                $newUser = new User();
                $newUser->setUsername($ldapUserInfo['username']);
                $newUser->setEmail($ldapUserInfo['email'] ?? null);
                $newUser->setNom($nom);
                $newUser->setPrenom($prenom);
                $newUser->setRoles($roles);
                $newUser->setSecteur($readableDepartment);
                $newUser->setManager($ldapUserInfo['manager']['distinguishedName'] ?? null);
                $newUser->setIsManager($ldapUserInfo['isManager'] ?? null);
                $newUser->setTitre($ldapUserInfo['title'] ?? null);
                $newUser->setHoraireHebdo(35.0);
                $entityManager->persist($newUser);
                $entityManager->flush();
            }
        }
    }
}
