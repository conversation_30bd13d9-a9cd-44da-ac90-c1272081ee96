import { startStimulusApp } from '@symfony/stimulus-bundle';

// Import des controllers personnalisés organisés par dossiers

// Contrôleurs partagés
import ModalSelectionController from './controllers/shared/modal_selection_controller.js';

// Contrôleurs Dashboard
import MainSelectionController from './controllers/dashboard/main_selection_controller.js';

// Contrôleurs Missions
import MissionManagementController from './controllers/missions/mission_management_controller.js';
import MissionModalController from './controllers/missions/mission_modal_controller.js';
import MissionUserSelectionController from './controllers/missions/mission_user_selection_controller.js';

// Contrôleurs Users
import UserManagementController from './controllers/users/user_management_controller.js';

// Contrôleurs Heures
import HeuresManagementController from './controllers/heures/heures_management_controller.js';

// Contrôleurs Primes
import PrimesManagementController from './controllers/primes/primes_management_controller.js';

// Contrôleurs Calendar
import CalendarController from './controllers/calendar/calendar_controller.js';

const app = startStimulusApp();

// Configuration Stimulus
app.debug = window.location.hostname === 'localhost' || window.location.hostname.includes('127.0.0.1');

// Gestion des erreurs Stimulus
app.handleError = (error, message, detail) => {
    console.error('Erreur Stimulus:', message, error, detail);
    // En production, on peut envoyer l'erreur à un service de monitoring
    if (!app.debug && window.errorReporting) {
        window.errorReporting.captureException(error, { extra: { message, detail } });
    }
};

// Enregistrement des controllers personnalisés
app.register('main-selection', MainSelectionController);
app.register('modal-selection', ModalSelectionController);
app.register('user-management', UserManagementController);
app.register('mission-management', MissionManagementController);
app.register('mission-modal', MissionModalController);
app.register('mission-user-selection', MissionUserSelectionController);
app.register('calendar', CalendarController);
app.register('heures-management', HeuresManagementController);
app.register('primes-management', PrimesManagementController);
