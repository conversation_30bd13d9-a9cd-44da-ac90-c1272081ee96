<?php

namespace App\Repository;

use App\Entity\Mission;
use App\Entity\User;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<Mission>
 */
class MissionRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, Mission::class);
    }

    public function save(Mission $entity, bool $flush = false): void
    {
        $this->getEntityManager()->persist($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

    public function remove(Mission $entity, bool $flush = false): void
    {
        $this->getEntityManager()->remove($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

    /**
     * Trouve les missions actives à une date donnée
     */
    public function findActivesAuDate(\DateTimeInterface $date): array
    {
        return $this->createQueryBuilder('m')
            ->andWhere('m.dateDebut <= :date AND m.dateFin >= :date')
            ->setParameter('date', $date)
            ->orderBy('m.dateDebut', 'ASC')
            ->getQuery()
            ->getResult();
    }

    /**
     * Trouve les missions d'un utilisateur
     */
    public function findByUser(User $user): array
    {
        return $this->createQueryBuilder('m')
            ->join('m.users', 'u')
            ->andWhere('u = :user')
            ->setParameter('user', $user)
            ->orderBy('m.dateDebut', 'DESC')
            ->getQuery()
            ->getResult();
    }

    /**
     * Trouve les missions d'un utilisateur avec des critères supplémentaires
     */
    public function findByUserAndCriteria(User $user, array $criteria): array
    {
        $qb = $this->createQueryBuilder('m')
            ->join('m.users', 'u')
            ->andWhere('u = :user')
            ->setParameter('user', $user);

        // Appliquer les critères
        if (isset($criteria['zone'])) {
            $qb->andWhere('m.zone = :zone')
               ->setParameter('zone', $criteria['zone']);
        }

        if (isset($criteria['pays'])) {
            $qb->andWhere('m.pays = :pays')
               ->setParameter('pays', $criteria['pays']);
        }

        if (isset($criteria['niveau'])) {
            $qb->andWhere('m.niveau = :niveau')
               ->setParameter('niveau', $criteria['niveau']);
        }

        if (isset($criteria['dateDebut']) && isset($criteria['dateFin'])) {
            // Utiliser la même logique que findInPeriod pour gérer les chevauchements
            $qb->andWhere('(m.dateDebut BETWEEN :dateDebut AND :dateFin) OR (m.dateFin BETWEEN :dateDebut AND :dateFin) OR (m.dateDebut <= :dateDebut AND m.dateFin >= :dateFin)')
               ->setParameter('dateDebut', $criteria['dateDebut'])
               ->setParameter('dateFin', $criteria['dateFin']);
        }

        return $qb->orderBy('m.dateDebut', 'DESC')
                  ->getQuery()
                  ->getResult();
    }

    /**
     * Trouve les missions par zone
     */
    public function findByZone(string $zone): array
    {
        return $this->createQueryBuilder('m')
            ->andWhere('m.zone = :zone')
            ->setParameter('zone', $zone)
            ->orderBy('m.dateDebut', 'DESC')
            ->getQuery()
            ->getResult();
    }

    /**
     * Trouve les missions par pays
     */
    public function findByPays(string $pays): array
    {
        return $this->createQueryBuilder('m')
            ->andWhere('m.pays = :pays')
            ->setParameter('pays', $pays)
            ->orderBy('m.dateDebut', 'DESC')
            ->getQuery()
            ->getResult();
    }

    /**
     * Trouve les missions dans une période
     */
    public function findInPeriod(\DateTimeInterface $debut, \DateTimeInterface $fin): array
    {
        return $this->createQueryBuilder('m')
            ->andWhere('(m.dateDebut BETWEEN :debut AND :fin) OR (m.dateFin BETWEEN :debut AND :fin) OR (m.dateDebut <= :debut AND m.dateFin >= :fin)')
            ->setParameter('debut', $debut)
            ->setParameter('fin', $fin)
            ->orderBy('m.dateDebut', 'ASC')
            ->getQuery()
            ->getResult();
    }

    /**
     * Trouve les missions avec leurs segments
     */
    public function findWithSegments(): array
    {
        return $this->createQueryBuilder('m')
            ->leftJoin('m.segments', 's')
            ->addSelect('s')
            ->orderBy('m.dateDebut', 'DESC')
            ->getQuery()
            ->getResult();
    }

    /**
     * Statistiques des missions
     */
    public function getStatistiques(): array
    {
        $qb = $this->createQueryBuilder('m');

        $total = $qb->select('COUNT(m.id)')->getQuery()->getSingleScalarResult();

        $parZone = $this->createQueryBuilder('m')
            ->select('m.zone, COUNT(m.id) as count')
            ->groupBy('m.zone')
            ->getQuery()
            ->getResult();

        $parNiveau = $this->createQueryBuilder('m')
            ->select('m.niveau, COUNT(m.id) as count')
            ->groupBy('m.niveau')
            ->getQuery()
            ->getResult();

        return [
            'total' => $total,
            'parZone' => $parZone,
            'parNiveau' => $parNiveau
        ];
    }

    /**
     * Trouve les missions en cours
     */
    public function findEnCours(): array
    {
        $today = new \DateTime();
        return $this->findActivesAuDate($today);
    }

    /**
     * Trouve les prochaines missions
     */
    public function findProchaines(int $limit = 10): array
    {
        $today = new \DateTime();

        return $this->createQueryBuilder('m')
            ->andWhere('m.dateDebut > :today')
            ->setParameter('today', $today)
            ->orderBy('m.dateDebut', 'ASC')
            ->setMaxResults($limit)
            ->getQuery()
            ->getResult();
    }

    /**
     * Recherche avec critères multiples
     */
    public function findWithCriteria(array $criteria): array
    {
        $qb = $this->createQueryBuilder('m')
            ->leftJoin('m.users', 'u')
            ->addSelect('u');

        if (isset($criteria['zone'])) {
            $qb->andWhere('m.zone = :zone')
               ->setParameter('zone', $criteria['zone']);
        }

        if (isset($criteria['pays'])) {
            $qb->andWhere('m.pays = :pays')
               ->setParameter('pays', $criteria['pays']);
        }

        if (isset($criteria['niveau'])) {
            $qb->andWhere('m.niveau = :niveau')
               ->setParameter('niveau', $criteria['niveau']);
        }

        if (isset($criteria['user'])) {
            $qb->andWhere('u = :user')
               ->setParameter('user', $criteria['user']);
        }

        if (isset($criteria['dateDebut']) && isset($criteria['dateFin'])) {
            // Utiliser la même logique que findInPeriod pour gérer les chevauchements
            $qb->andWhere('(m.dateDebut BETWEEN :dateDebut AND :dateFin) OR (m.dateFin BETWEEN :dateDebut AND :dateFin) OR (m.dateDebut <= :dateDebut AND m.dateFin >= :dateFin)')
               ->setParameter('dateDebut', $criteria['dateDebut'])
               ->setParameter('dateFin', $criteria['dateFin']);
        }

        return $qb->orderBy('m.dateDebut', 'DESC')
                  ->getQuery()
                  ->getResult();
    }

    /**
     * Recherche avec critères multiples et pagination
     */
    public function findWithCriteriaAndPagination(array $criteria, int $page = 1, int $limit = 10): array
    {
        // Première requête pour compter le total sans jointure
        $countQb = $this->createQueryBuilder('m');

        // Recherche par titre ou pays
        if (isset($criteria['search']) && !empty($criteria['search'])) {
            $countQb->andWhere('m.titre LIKE :search OR m.pays LIKE :search')
                    ->setParameter('search', '%' . $criteria['search'] . '%');
        }

        // Filtre par zone
        if (isset($criteria['zone']) && !empty($criteria['zone'])) {
            $countQb->andWhere('m.zone = :zone')
                    ->setParameter('zone', $criteria['zone']);
        }

        // Filtre par niveau
        if (isset($criteria['niveau']) && !empty($criteria['niveau'])) {
            $countQb->andWhere('m.niveau = :niveau')
                    ->setParameter('niveau', (int) $criteria['niveau']);
        }

        // Filtre par statut
        if (isset($criteria['statut']) && !empty($criteria['statut'])) {
            $today = new \DateTime();
            switch ($criteria['statut']) {
                case 'en_cours':
                    $countQb->andWhere('m.dateDebut <= :today AND m.dateFin >= :today')
                            ->setParameter('today', $today);
                    break;
                case 'prochaines':
                    $countQb->andWhere('m.dateDebut > :today')
                            ->setParameter('today', $today);
                    break;
                case 'terminees':
                    $countQb->andWhere('m.dateFin < :today')
                            ->setParameter('today', $today);
                    break;
            }
        }

        // Compter le total
        $total = $countQb->select('COUNT(m.id)')
                        ->getQuery()
                        ->getSingleScalarResult();

        // Deuxième requête pour récupérer les IDs des missions sans doublons
        $qb = $this->createQueryBuilder('m');

        // Appliquer les mêmes critères
        if (isset($criteria['search']) && !empty($criteria['search'])) {
            $qb->andWhere('m.titre LIKE :search OR m.pays LIKE :search')
               ->setParameter('search', '%' . $criteria['search'] . '%');
        }

        if (isset($criteria['zone']) && !empty($criteria['zone'])) {
            $qb->andWhere('m.zone = :zone')
               ->setParameter('zone', $criteria['zone']);
        }

        if (isset($criteria['niveau']) && !empty($criteria['niveau'])) {
            $qb->andWhere('m.niveau = :niveau')
               ->setParameter('niveau', (int) $criteria['niveau']);
        }

        if (isset($criteria['statut']) && !empty($criteria['statut'])) {
            $today = new \DateTime();
            switch ($criteria['statut']) {
                case 'en_cours':
                    $qb->andWhere('m.dateDebut <= :today AND m.dateFin >= :today')
                       ->setParameter('today', $today);
                    break;
                case 'prochaines':
                    $qb->andWhere('m.dateDebut > :today')
                       ->setParameter('today', $today);
                    break;
                case 'terminees':
                    $qb->andWhere('m.dateFin < :today')
                       ->setParameter('today', $today);
                    break;
            }
        }

        // Récupérer les IDs des missions avec pagination
        $offset = ($page - 1) * $limit;
        $missionIds = $qb->select('m.id')
                        ->orderBy('m.dateFin', 'DESC')
                        ->addOrderBy('m.id', 'DESC')
                        ->setFirstResult($offset)
                        ->setMaxResults($limit)
                        ->getQuery()
                        ->getArrayResult();

        // Extraire les IDs
        $ids = array_column($missionIds, 'id');

        // Troisième requête pour récupérer les missions complètes avec leurs relations
        if (empty($ids)) {
            $missions = [];
        } else {
            $missions = $this->createQueryBuilder('m')
                ->leftJoin('m.users', 'u')
                ->leftJoin('m.client', 'c')
                ->leftJoin('m.site', 's')
                ->addSelect('u', 'c', 's')
                ->where('m.id IN (:ids)')
                ->setParameter('ids', $ids)
                ->orderBy('m.dateFin', 'DESC')
                ->addOrderBy('m.id', 'DESC')
                ->getQuery()
                ->getResult();
        }

        return [
            'missions' => $missions,
            'total' => $total,
            'page' => $page,
            'limit' => $limit,
            'totalPages' => ceil($total / $limit)
        ];
    }
}
