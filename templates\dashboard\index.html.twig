{% extends 'base.html.twig' %}

{% block title %}Dashboard - OSI Manager{% endblock %}

{% block body %}
<div class="px-4 py-6 sm:px-0">
    <!-- En-tête -->
    <div class="mb-8">
        <h1 class="text-3xl font-bold text-gray-900">Dashboard</h1>
        <p class="mt-2 text-gray-600">Vue d'ensemble des missions et utilisateurs</p>
    </div>

    <!-- Statistiques générales -->
    <div class="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4 mb-8 mx-auto">
        <!-- Total Utilisateurs -->
        {% if not app.user.isUser() %}
            
            <div class="bg-white overflow-hidden shadow rounded-lg">
                <div class="p-5">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="w-8 h-8 bg-blue-500 rounded-md flex items-center justify-center">
                                <span class="text-white text-sm font-medium">👥</span>
                            </div>
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-500 truncate">Utilisateurs</dt>
                                <dd class="text-lg font-medium text-gray-900">{{ statsCollaborateurs.total }}</dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Forfait Jour -->
            <div class="bg-white overflow-hidden shadow rounded-lg">
                <div class="p-5">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="w-8 h-8 bg-green-500 rounded-md flex items-center justify-center">
                                <span class="text-white text-sm font-medium">📅</span>
                            </div>
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-500 truncate">Forfait Jour</dt>
                                <dd class="text-lg font-medium text-gray-900">{{ statsCollaborateurs.forfaitJour }}</dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>
        {% endif %}

        <!-- Total Missions -->
        <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-purple-500 rounded-md flex items-center justify-center">
                            <span class="text-white text-sm font-medium">🌍</span>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">Total Missions</dt>
                            <dd class="text-lg font-medium text-gray-900">{{ statsMissions.total }}</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <!-- Missions en cours -->
        <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-orange-500 rounded-md flex items-center justify-center">
                            <span class="text-white text-sm font-medium">🚀</span>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">En cours</dt>
                            <dd class="text-lg font-medium text-gray-900">{{ missionsEnCours|length }}</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Contenu principal -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
        <!-- Missions en cours -->
        <div class="bg-white shadow rounded-lg">
            <div class="px-4 py-5 sm:p-6">
                <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Missions en cours</h3>
                {% if missionsEnCours|length > 0 %}
                    <div class="space-y-3">
                        {% for mission in missionsEnCours %}
                            <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                                <div>
                                    <p class="text-sm font-medium text-gray-900">{{ mission.titre }}</p>
                                    <p class="text-sm text-gray-500">
                                        {% if mission.users|length > 0 %}
                                            {% for user in mission.users %}
                                                {{ user.nomComplet }}{% if not loop.last %}, {% endif %}
                                            {% endfor %}
                                        {% else %}
                                            Aucun utilisateur assigné
                                        {% endif %}
                                        - {{ mission.pays }}
                                    </p>
                                </div>
                                <div class="text-right">
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                        {% if mission.zone == 'EURO' %}bg-blue-100 text-blue-800{% else %}bg-green-100 text-green-800{% endif %}">
                                        {{ mission.zone == 'EURO' ? 'Zone Euro' : 'Hors Zone Euro' }}
                                    </span>
                                    <p class="text-xs text-gray-500 mt-1">Niveau {{ mission.niveau }}</p>
                                </div>
                            </div>
                        {% endfor %}
                    </div>
                {% else %}
                    <p class="text-gray-500 text-center py-4">Aucune mission en cours</p>
                {% endif %}
                <div class="mt-4">
                    <a href="{{ path('app_missions') }}" class="text-sm font-medium text-blue-600 hover:text-blue-500">
                        Voir toutes les missions →
                    </a>
                </div>
            </div>
        </div>

        <!-- Prochaines missions -->
        <div class="bg-white shadow rounded-lg">
            <div class="px-4 py-5 sm:p-6">
                <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Prochaines missions</h3>
                {% if prochaines|length > 0 %}
                    <div class="space-y-3">
                        {% for mission in prochaines %}
                            <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                                <div>
                                    <p class="text-sm font-medium text-gray-900">{{ mission.titre }}</p>
                                    <p class="text-sm text-gray-500">
                                        {% if mission.users|length > 0 %}
                                            {% for user in mission.users %}
                                                {{ user.nomComplet }}{% if not loop.last %}, {% endif %}
                                            {% endfor %}
                                        {% else %}
                                            Aucun utilisateur assigné
                                        {% endif %}
                                        - {{ mission.pays }}
                                    </p>
                                </div>
                                <div class="text-right">
                                    <p class="text-sm font-medium text-gray-900">{{ mission.dateDebut|date('d/m/Y') }}</p>
                                    <p class="text-xs text-gray-500">{{ mission.dureeJours }} jour(s)</p>
                                </div>
                            </div>
                        {% endfor %}
                    </div>
                {% else %}
                    <p class="text-gray-500 text-center py-4">Aucune mission prochaine</p>
                {% endif %}
                <div class="mt-4">
                    <a href="{{ path('app_calendrier') }}" class="text-sm font-medium text-blue-600 hover:text-blue-500">
                        Voir le calendrier →
                    </a>
                </div>
            </div>
        </div>
    </div>

        <!-- Actions rapides -->
    <div class="mt-8 bg-white shadow rounded-lg">
        <div class="px-4 py-5 sm:p-6">
            <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Actions rapides</h3>
            <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
                <a href="{{ path('app_calendrier') }}" class="flex items-center p-4 bg-blue-50 rounded-lg hover:bg-blue-100 transition-colors">
                    <span class="text-2xl mr-3">📅</span>
                    <div>
                        <p class="text-sm font-medium text-blue-900">Calendrier</p>
                        <p class="text-xs text-blue-700">Gérer les segments</p>
                    </div>
                </a>
                <a href="{{ path('app_users') }}" class="flex items-center p-4 bg-green-50 rounded-lg hover:bg-green-100 transition-colors">
                    <span class="text-2xl mr-3">👥</span>
                    <div>
                        <p class="text-sm font-medium text-green-900">Utilisateurs</p>
                        <p class="text-xs text-green-700">Gérer l'équipe</p>
                    </div>
                </a>
                <a href="{{ path('app_heures') }}" class="flex items-center p-4 bg-purple-50 rounded-lg hover:bg-purple-100 transition-colors">
                    <span class="text-2xl mr-3">⏰</span>
                    <div>
                        <p class="text-sm font-medium text-purple-900">Heures</p>
                        <p class="text-xs text-purple-700">Suivi du temps</p>
                    </div>
                </a>
                <a href="{{ path('app_primes') }}" class="flex items-center p-4 bg-orange-50 rounded-lg hover:bg-orange-100 transition-colors">
                    <span class="text-2xl mr-3">💰</span>
                    <div>
                        <p class="text-sm font-medium text-orange-900">Primes</p>
                        <p class="text-xs text-orange-700">Calcul des primes</p>
                    </div>
                </a>
            </div>
        </div>
    </div>


    <!-- Utilisateurs actifs -->
    {% if not app.user.isUser() %}
        {% if usersActifs|length > 0 %}
            <div class="mt-8 bg-white shadow rounded-lg">
                <div class="px-4 py-5 sm:p-6">
                    <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Utilisateurs en mission</h3>
                    <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
                        {% for user in usersActifs %}
                            <div class="flex items-center p-3 bg-gray-50 rounded-lg">
                                <div class="flex-shrink-0">
                                    <div class="w-10 h-10 bg-blue-500 rounded-full flex items-center justify-center">
                                        <span class="text-white font-medium">{{ user.prenom|first }}{{ user.nom|first }}</span>
                                    </div>
                                </div>
                                <div class="ml-3">
                                    <p class="text-sm font-medium text-gray-900">{{ user.nomComplet }}</p>
                                    <p class="text-sm text-gray-500">{{ user.roleDisplay }}</p>
                                </div>
                            </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
        {% endif %}
    {% endif %}

</div>
{% endblock %}
