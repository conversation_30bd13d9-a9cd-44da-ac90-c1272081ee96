{% extends 'base.html.twig' %}

{% block title %}Dashboard - OSI Manager{% endblock %}

{% block body %}
<div class="px-4 py-6 sm:px-0">
    <!-- En-tête clair et moderne -->
    <div class="mb-8 relative overflow-hidden bg-gradient-to-r from-blue-50 to-indigo-50 rounded-2xl p-8 shadow-sm border border-blue-100">
        <div class="relative z-10">
            <h1 class="text-4xl font-bold mb-2 text-gray-800">
                Dashboard
            </h1>
            <p class="text-gray-600 text-lg">Vue d'ensemble des missions et utilisateurs</p>
            <div class="mt-4 flex items-center space-x-4">
                <div class="flex items-center space-x-2">
                    <div class="w-3 h-3 bg-green-500 rounded-full animate-pulse"></div>
                    <span class="text-sm text-gray-600">Système opérationnel</span>
                </div>
                <div class="text-sm text-gray-500">
                    {{ "now"|date("d/m/Y à H:i") }}
                </div>
            </div>
        </div>
        <!-- Motif décoratif subtil -->
        <div class="absolute top-0 right-0 -mt-4 -mr-4 w-32 h-32 bg-white opacity-40 rounded-full"></div>
        <div class="absolute bottom-0 left-0 -mb-8 -ml-8 w-24 h-24 bg-white opacity-30 rounded-full"></div>
    </div>

    <!-- Statistiques générales avec design moderne -->
    {% if not app.user.isUser() %}
        <div class="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-4 mb-8">
    {% else %}
        <div class="flex justify-center gap-6 mb-8 mx-auto max-w-4xl">
    {% endif %}
        <!-- Total Utilisateurs -->
        {% if not app.user.isUser() %}
            <div class="group relative bg-white overflow-hidden shadow-md rounded-xl border border-blue-100 hover:shadow-lg transition-all duration-300 hover:-translate-y-1">
                <div class="relative p-6">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center space-x-4">
                            <div class="w-12 h-12 bg-blue-500 rounded-xl flex items-center justify-center shadow-md group-hover:scale-110 transition-transform duration-300">
                                <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
                                </svg>
                            </div>
                            <div>
                                <p class="text-sm font-medium text-gray-600 mb-1">Utilisateurs</p>
                                <p class="text-2xl font-bold text-gray-800">{{ statsCollaborateurs.total }}</p>
                            </div>
                        </div>
                        <div class="text-right">
                            <div class="flex items-center text-green-600 text-sm font-medium">
                                <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
                                </svg>
                                Actif
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Forfait Jour -->
            <div class="group relative bg-white overflow-hidden shadow-md rounded-xl border border-green-100 hover:shadow-lg transition-all duration-300 hover:-translate-y-1">
                <div class="relative p-6">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center space-x-4">
                            <div class="w-12 h-12 bg-green-500 rounded-xl flex items-center justify-center shadow-md group-hover:scale-110 transition-transform duration-300">
                                <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                                </svg>
                            </div>
                            <div>
                                <p class="text-sm font-medium text-gray-600 mb-1">Forfait Jour</p>
                                <p class="text-2xl font-bold text-gray-800">{{ statsCollaborateurs.forfaitJour }}</p>
                            </div>
                        </div>
                        <div class="text-right">
                            <div class="flex items-center text-blue-600 text-sm font-medium">
                                <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                                Configuré
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        {% endif %}

        <!-- Total Missions -->
        <div class="group relative bg-white overflow-hidden shadow-md rounded-xl border border-purple-100 hover:shadow-lg transition-all duration-300 hover:-translate-y-1">
            <div class="relative p-6">
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-4">
                        <div class="w-12 h-12 bg-purple-500 rounded-xl flex items-center justify-center shadow-md group-hover:scale-110 transition-transform duration-300">
                            <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3.055 11H5a2 2 0 012 2v1a2 2 0 002 2 2 2 0 012 2v2.945M8 3.935V5.5A2.5 2.5 0 0010.5 8h.5a2 2 0 012 2 2 2 0 104 0 2 2 0 012-2h1.064M15 20.488V18a2 2 0 012-2h3.064M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                        </div>
                        <div>
                            <p class="text-sm font-medium text-gray-600 mb-1">Total Missions</p>
                            <p class="text-2xl font-bold text-gray-800">{{ statsMissions.total }}</p>
                        </div>
                    </div>
                    <div class="text-right">
                        <div class="flex items-center text-purple-600 text-sm font-medium">
                            <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                            </svg>
                            Global
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Missions en cours -->
        <div class="group relative bg-white overflow-hidden shadow-md rounded-xl border border-orange-100 hover:shadow-lg transition-all duration-300 hover:-translate-y-1">
            <div class="relative p-6">
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-4">
                        <div class="w-12 h-12 bg-orange-500 rounded-xl flex items-center justify-center shadow-md group-hover:scale-110 transition-transform duration-300">
                            <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                            </svg>
                        </div>
                        <div>
                            <p class="text-sm font-medium text-gray-600 mb-1">En cours</p>
                            <p class="text-2xl font-bold text-gray-800">{{ missionsEnCours|length }}</p>
                        </div>
                    </div>
                    <div class="text-right">
                        <div class="flex items-center text-green-600 text-sm font-medium">
                            <div class="w-2 h-2 bg-green-500 rounded-full animate-pulse mr-2"></div>
                            Actives
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>



    <!-- Contenu principal -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
        <!-- Missions en cours -->
        <div class="bg-white shadow rounded-lg">
            <div class="px-4 py-5 sm:p-6">
                <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Missions en cours</h3>
                {% if missionsEnCours|length > 0 %}
                    <div class="space-y-3">
                        {% for mission in missionsEnCours %}
                            <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                                <div>
                                    <p class="text-sm font-medium text-gray-900">{{ mission.titre }}</p>
                                    <div class="flex items-center space-x-2 text-sm text-gray-500">
                                        {% if mission.users|length > 0 %}
                                            <div class="flex items-center space-x-1">
                                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                                                </svg>
                                                <span>
                                                    {% for user in mission.users|slice(0, 2) %}
                                                        {{ user.nomComplet }}{% if not loop.last %}, {% endif %}
                                                    {% endfor %}
                                                    {% if mission.users|length > 2 %}
                                                        <span class="relative inline-block ml-1">
                                                            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800 cursor-help hover:bg-blue-200 transition-colors user-tooltip"
                                                                  data-users="{% for user in mission.users|slice(2) %}{{ user.nomComplet }}{% if not loop.last %}, {% endif %}{% endfor %}">
                                                                +{{ mission.users|length - 2 }}
                                                            </span>
                                                        </span>
                                                    {% endif %}
                                                </span>
                                            </div>
                                        {% else %}
                                            <div class="flex items-center space-x-1 text-orange-600">
                                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                                                </svg>
                                                <span>Aucun utilisateur assigné</span>
                                            </div>
                                        {% endif %}
                                        <span class="text-gray-400">•</span>
                                        <div class="flex items-center space-x-1">
                                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                            </svg>
                                            <span>{{ mission.pays }}</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="text-right">
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                        {% if mission.zone == 'EURO' %}bg-blue-100 text-blue-800{% else %}bg-green-100 text-green-800{% endif %}">
                                        {{ mission.zone == 'EURO' ? 'Zone Euro' : 'Hors Zone Euro' }}
                                    </span>
                                    <p class="text-xs text-gray-500 mt-1">Niveau {{ mission.niveau }}</p>
                                </div>
                            </div>
                        {% endfor %}
                    </div>
                {% else %}
                    <p class="text-gray-500 text-center py-4">Aucune mission en cours</p>
                {% endif %}
                <div class="mt-4">
                    <a href="{{ path('app_missions') }}" class="text-sm font-medium text-blue-600 hover:text-blue-500">
                        Voir toutes les missions →
                    </a>
                </div>
            </div>
        </div>

        <!-- Prochaines missions -->
        <div class="bg-white shadow rounded-lg">
            <div class="px-4 py-5 sm:p-6">
                <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Prochaines missions</h3>
                {% if prochaines|length > 0 %}
                    <div class="space-y-3">
                        {% for mission in prochaines %}
                            <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                                <div>
                                    <p class="text-sm font-medium text-gray-900">{{ mission.titre }}</p>
                                    <div class="flex items-center space-x-2 text-sm text-gray-500">
                                        {% if mission.users|length > 0 %}
                                            <div class="flex items-center space-x-1">
                                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                                                </svg>
                                                <span>
                                                    {% for user in mission.users|slice(0, 2) %}
                                                        {{ user.nomComplet }}{% if not loop.last %}, {% endif %}
                                                    {% endfor %}
                                                    {% if mission.users|length > 2 %}
                                                        <span class="relative inline-block ml-1">
                                                            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800 cursor-help hover:bg-green-200 transition-colors user-tooltip"
                                                                  data-users="{% for user in mission.users|slice(2) %}{{ user.nomComplet }}{% if not loop.last %}, {% endif %}{% endfor %}">
                                                                +{{ mission.users|length - 2 }}
                                                            </span>
                                                        </span>
                                                    {% endif %}
                                                </span>
                                            </div>
                                        {% else %}
                                            <div class="flex items-center space-x-1 text-orange-600">
                                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                                                </svg>
                                                <span>Aucun utilisateur assigné</span>
                                            </div>
                                        {% endif %}
                                        <span class="text-gray-400">•</span>
                                        <div class="flex items-center space-x-1">
                                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                            </svg>
                                            <span>{{ mission.pays }}</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="text-right">
                                    <p class="text-sm font-medium text-gray-900">{{ mission.dateDebut|date('d/m/Y') }}</p>
                                    <p class="text-xs text-gray-500">{{ mission.dureeJours }} jour(s)</p>
                                </div>
                            </div>
                        {% endfor %}
                    </div>
                {% else %}
                    <p class="text-gray-500 text-center py-4">Aucune mission prochaine</p>
                {% endif %}
                <div class="mt-4">
                    <a href="{{ path('app_calendrier') }}" class="text-sm font-medium text-blue-600 hover:text-blue-500">
                        Voir le calendrier →
                    </a>
                </div>
            </div>
        </div>
    </div>

        <!-- Actions rapides -->
    <div class="mt-8 bg-white shadow rounded-lg">
        <div class="px-4 py-5 sm:p-6">
            <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Actions rapides</h3>
            <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
                <a href="{{ path('app_calendrier') }}" class="flex items-center p-4 bg-blue-50 rounded-lg hover:bg-blue-100 transition-colors">
                    <span class="text-2xl mr-3">📅</span>
                    <div>
                        <p class="text-sm font-medium text-blue-900">Calendrier</p>
                        <p class="text-xs text-blue-700">Gérer les segments</p>
                    </div>
                </a>
                <a href="{{ path('app_users') }}" class="flex items-center p-4 bg-green-50 rounded-lg hover:bg-green-100 transition-colors">
                    <span class="text-2xl mr-3">👥</span>
                    <div>
                        <p class="text-sm font-medium text-green-900">Utilisateurs</p>
                        <p class="text-xs text-green-700">Gérer l'équipe</p>
                    </div>
                </a>
                <a href="{{ path('app_heures') }}" class="flex items-center p-4 bg-purple-50 rounded-lg hover:bg-purple-100 transition-colors">
                    <span class="text-2xl mr-3">⏰</span>
                    <div>
                        <p class="text-sm font-medium text-purple-900">Heures</p>
                        <p class="text-xs text-purple-700">Suivi du temps</p>
                    </div>
                </a>
                <a href="{{ path('app_primes') }}" class="flex items-center p-4 bg-orange-50 rounded-lg hover:bg-orange-100 transition-colors">
                    <span class="text-2xl mr-3">💰</span>
                    <div>
                        <p class="text-sm font-medium text-orange-900">Primes</p>
                        <p class="text-xs text-orange-700">Calcul des primes</p>
                    </div>
                </a>
            </div>
        </div>
    </div>


    <!-- Utilisateurs actifs -->
    {% if not app.user.isUser() %}
        {% if usersActifs|length > 0 %}
            <div class="mt-8 bg-white shadow-xl rounded-2xl border border-gray-100 overflow-hidden">
                <div class="bg-gradient-to-r from-teal-500 to-cyan-600 px-6 py-4">
                    <h3 class="text-xl font-bold text-white flex items-center">
                        <svg class="w-6 h-6 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                        </svg>
                        Utilisateurs en mission
                    </h3>
                </div>
                <div class="p-6">
                    <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
                        {% for user in usersActifs %}
                            <div class="group relative bg-gradient-to-br from-gray-50 to-teal-50 border border-gray-200 rounded-xl p-4 hover:shadow-lg transition-all duration-300 hover:border-teal-300">
                                <div class="flex items-center space-x-4">
                                    <div class="relative flex-shrink-0">
                                        <div class="w-12 h-12 bg-gradient-to-br from-teal-500 to-cyan-600 rounded-full flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform duration-300">
                                            <span class="text-white font-bold text-sm">{{ user.prenom|first }}{{ user.nom|first }}</span>
                                        </div>
                                        <!-- Indicateur de statut en ligne -->
                                        <div class="absolute -bottom-1 -right-1 w-4 h-4 bg-green-500 border-2 border-white rounded-full animate-pulse"></div>
                                    </div>
                                    <div class="flex-1 min-w-0">
                                        <h4 class="text-sm font-bold text-gray-900 group-hover:text-teal-700 transition-colors truncate">
                                            {{ user.nomComplet }}
                                        </h4>
                                        <div class="flex items-center space-x-1 mt-1">
                                            <svg class="w-3 h-3 text-teal-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 13.255A23.931 23.931 0 0112 15c-3.183 0-6.22-.62-9-1.745M16 6V4a2 2 0 00-2-2h-4a2 2 0 00-2-2v2m8 0V6a2 2 0 012 2v6a2 2 0 01-2 2H8a2 2 0 01-2-2V8a2 2 0 012-2z"></path>
                                            </svg>
                                            <p class="text-xs text-gray-600 truncate">{{ user.roleDisplay }}</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
        {% endif %}
    {% endif %}

</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    initUserTooltips();
});

// Réinitialiser les tooltips après navigation Turbo
document.addEventListener('turbo:load', function() {
    initUserTooltips();
});

function initUserTooltips() {
    // Nettoyer les anciens event listeners
    const existingTooltips = document.querySelectorAll('.custom-tooltip');
    existingTooltips.forEach(tooltip => tooltip.remove());

    const userTooltips = document.querySelectorAll('.user-tooltip');

    userTooltips.forEach(badge => {
        // Supprimer les anciens event listeners
        badge.removeEventListener('mouseenter', handleMouseEnter);
        badge.removeEventListener('mouseleave', handleMouseLeave);

        // Ajouter les nouveaux event listeners
        badge.addEventListener('mouseenter', handleMouseEnter);
        badge.addEventListener('mouseleave', handleMouseLeave);
    });
}

function handleMouseEnter(e) {
    const badge = e.currentTarget;

    // Supprimer tout tooltip existant
    const existingTooltip = document.querySelector('.custom-tooltip');
    if (existingTooltip) {
        existingTooltip.remove();
    }

    const users = badge.getAttribute('data-users');
    if (!users || users.trim() === '') return;

    // Créer le tooltip
    const tooltip = document.createElement('div');
    tooltip.className = 'custom-tooltip absolute z-50 px-4 py-3 text-sm text-white bg-gray-900 rounded-lg shadow-lg';
    tooltip.style.bottom = '100%';
    tooltip.style.left = '50%';
    tooltip.style.transform = 'translateX(-50%)';
    tooltip.style.marginBottom = '8px';
    tooltip.style.pointerEvents = 'none'; // Éviter les conflits de survol
    tooltip.style.minWidth = '200px'; // Largeur minimale
    tooltip.style.maxWidth = '400px'; // Largeur maximale
    tooltip.style.whiteSpace = 'nowrap'; // Empêcher le retour à la ligne
    tooltip.style.overflow = 'hidden'; // Masquer le débordement si nécessaire
    tooltip.style.textOverflow = 'ellipsis'; // Ajouter ... si trop long
    tooltip.textContent = users;

    // Ajouter une flèche
    const arrow = document.createElement('div');
    arrow.className = 'absolute top-full left-1/2 transform -translate-x-1/2';
    arrow.style.width = '0';
    arrow.style.height = '0';
    arrow.style.borderLeft = '4px solid transparent';
    arrow.style.borderRight = '4px solid transparent';
    arrow.style.borderTop = '4px solid #1f2937';
    tooltip.appendChild(arrow);

    // S'assurer que le parent a une position relative
    const container = badge.closest('.relative') || badge.parentElement;
    if (!container.style.position || container.style.position === 'static') {
        container.style.position = 'relative';
    }

    container.appendChild(tooltip);

    // Animation d'apparition immédiate
    requestAnimationFrame(() => {
        tooltip.style.opacity = '1';
        tooltip.style.transition = 'opacity 0.2s ease-in-out';
    });
}

function handleMouseLeave(e) {
    // Délai pour éviter le scintillement
    setTimeout(() => {
        const tooltip = document.querySelector('.custom-tooltip');
        if (tooltip) {
            tooltip.style.opacity = '0';
            setTimeout(() => {
                if (tooltip && tooltip.parentNode) {
                    tooltip.remove();
                }
            }, 200);
        }
    }, 50);
}

// Nettoyer les tooltips lors de la navigation Turbo
document.addEventListener('turbo:before-visit', function() {
    const tooltips = document.querySelectorAll('.custom-tooltip');
    tooltips.forEach(tooltip => tooltip.remove());
});
</script>

{% endblock %}
