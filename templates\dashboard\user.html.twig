{% extends 'base.html.twig' %}

{% block title %}Mon Dashboard{% endblock %}

{% block body %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 mb-0">Mon Dashboard</h1>
                <div class="btn-group">
                    <a href="{{ path('app_calendrier') }}" class="btn btn-primary">
                        <i class="fas fa-calendar"></i> Saisir mes heures
                    </a>
                </div>
            </div>

            <!-- Informations utilisateur -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="card bg-light">
                        <div class="card-body">
                            <h5 class="card-title">
                                <i class="fas fa-user"></i> {{ user.nomComplet }}
                            </h5>
                            <div class="row">
                                <div class="col-md-6">
                                    <p class="card-text">
                                        <strong>Email :</strong> {{ user.email }}<br>
                                        <strong>Secteur :</strong> {{ user.secteur ?? 'Non défini' }}<br>
                                        <strong>Manager :</strong> {{ user.manager ?? 'Non défini' }}
                                    </p>
                                </div>
                                <div class="col-md-6">
                                    <p class="card-text">
                                        <strong>Horaire hebdomadaire :</strong> {{ user.horaireHebdo ?? 'Non défini' }}h<br>
                                        <strong>Forfait jour :</strong> {{ user.forfaitJour ? 'Oui' : 'Non' }}<br>
                                        <strong>Téléphone :</strong> {{ user.telephone ?? 'Non défini' }}
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Actions rapides -->
            <div class="row mb-4">
                <div class="col-md-4 mb-3">
                    <div class="card h-100">
                        <div class="card-body text-center">
                            <i class="fas fa-calendar-plus fa-3x text-primary mb-3"></i>
                            <h5 class="card-title">Saisir mes heures</h5>
                            <p class="card-text">Enregistrer mes segments de travail</p>
                            <a href="{{ path('app_calendrier') }}" class="btn btn-primary">
                                <i class="fas fa-plus"></i> Saisir
                            </a>
                        </div>
                    </div>
                </div>
                <div class="col-md-4 mb-3">
                    <div class="card h-100">
                        <div class="card-body text-center">
                            <i class="fas fa-clock fa-3x text-info mb-3"></i>
                            <h5 class="card-title">Mes heures</h5>
                            <p class="card-text">Consulter l'historique de mes heures</p>
                            <a href="{{ path('app_heures') }}" class="btn btn-info">
                                <i class="fas fa-eye"></i> Consulter
                            </a>
                        </div>
                    </div>
                </div>
                <div class="col-md-4 mb-3">
                    <div class="card h-100">
                        <div class="card-body text-center">
                            <i class="fas fa-euro-sign fa-3x text-success mb-3"></i>
                            <h5 class="card-title">Mes primes</h5>
                            <p class="card-text">Voir mes primes calculées</p>
                            <a href="{{ path('app_primes') }}" class="btn btn-success">
                                <i class="fas fa-money-bill"></i> Voir
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Statistiques de mes segments -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="fas fa-chart-bar"></i> Mes statistiques
                            </h5>
                        </div>
                        <div class="card-body">
                            {% set segmentsValides = segments|filter(s => s.valide) %}
                            {% set segmentsEnAttente = segments|filter(s => not s.valide) %}
                            
                            <div class="row">
                                <div class="col-md-3">
                                    <div class="card bg-primary text-white">
                                        <div class="card-body text-center">
                                            <h4>{{ segments|length }}</h4>
                                            <p class="mb-0">Total segments</p>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="card bg-success text-white">
                                        <div class="card-body text-center">
                                            <h4>{{ segmentsValides|length }}</h4>
                                            <p class="mb-0">Validés</p>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="card bg-warning text-white">
                                        <div class="card-body text-center">
                                            <h4>{{ segmentsEnAttente|length }}</h4>
                                            <p class="mb-0">En attente</p>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="card bg-info text-white">
                                        <div class="card-body text-center">
                                            {% set totalHeures = 0 %}
                                            {% for segment in segments %}
                                                {% set totalHeures = totalHeures + (segment.dureeMinutes / 60) %}
                                            {% endfor %}
                                            <h4>{{ totalHeures|round(1) }}h</h4>
                                            <p class="mb-0">Total heures</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Mes derniers segments -->
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="fas fa-history"></i> Mes derniers segments
                            </h5>
                        </div>
                        <div class="card-body">
                            {% if segments|length > 0 %}
                                <div class="table-responsive">
                                    <table class="table table-striped">
                                        <thead>
                                            <tr>
                                                <th>Mission</th>
                                                <th>Type</th>
                                                <th>Début</th>
                                                <th>Fin</th>
                                                <th>Durée</th>
                                                <th>Statut</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            {% for segment in segments|slice(0, 10) %}
                                                <tr>
                                                    <td>{{ segment.mission.titre }}</td>
                                                    <td>
                                                        <span class="badge badge-{{ segment.type == 'VOYAGE' ? 'primary' : (segment.type == 'INTERVENTION' ? 'success' : 'info') }}">
                                                            {{ segment.type }}
                                                        </span>
                                                    </td>
                                                    <td>{{ segment.dateHeureDebut|date('d/m/Y H:i') }}</td>
                                                    <td>{{ segment.dateHeureFin|date('d/m/Y H:i') }}</td>
                                                    <td>{{ (segment.dureeMinutes / 60)|round(2) }}h</td>
                                                    <td>
                                                        {% if segment.valide %}
                                                            <span class="badge badge-success">
                                                                <i class="fas fa-check"></i> Validé
                                                            </span>
                                                            {% if segment.validePar %}
                                                                <br><small class="text-muted">par {{ segment.validePar.nomComplet }}</small>
                                                            {% endif %}
                                                        {% else %}
                                                            <span class="badge badge-warning">
                                                                <i class="fas fa-hourglass-half"></i> En attente
                                                            </span>
                                                        {% endif %}
                                                    </td>
                                                </tr>
                                            {% endfor %}
                                        </tbody>
                                    </table>
                                </div>
                                {% if segments|length > 10 %}
                                    <div class="text-center mt-3">
                                        <a href="{{ path('app_heures') }}" class="btn btn-outline-primary">
                                            Voir tous mes segments
                                        </a>
                                    </div>
                                {% endif %}
                            {% else %}
                                <div class="text-center py-4">
                                    <i class="fas fa-calendar-times fa-3x text-muted mb-3"></i>
                                    <h5>Aucun segment enregistré</h5>
                                    <p class="text-muted">Commencez par saisir vos heures de travail.</p>
                                    <a href="{{ path('app_calendrier') }}" class="btn btn-primary">
                                        <i class="fas fa-plus"></i> Saisir mes premières heures
                                    </a>
                                </div>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>

            <!-- Aide pour les utilisateurs -->
            <div class="row mt-4">
                <div class="col-12">
                    <div class="card border-info">
                        <div class="card-header bg-info text-white">
                            <h5 class="mb-0">
                                <i class="fas fa-question-circle"></i> Comment ça marche ?
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <h6>Saisie des heures :</h6>
                                    <ol>
                                        <li>Cliquez sur "Saisir mes heures"</li>
                                        <li>Sélectionnez la mission</li>
                                        <li>Choisissez le type de segment (Voyage, Intervention, Stand-by)</li>
                                        <li>Définissez les heures de début et fin</li>
                                        <li>Validez votre saisie</li>
                                    </ol>
                                </div>
                                <div class="col-md-6">
                                    <h6>Validation :</h6>
                                    <ul>
                                        <li><span class="badge badge-warning">En attente</span> : Vos heures sont en cours de validation</li>
                                        <li><span class="badge badge-success">Validé</span> : Vos heures ont été approuvées</li>
                                        <li>Les primes sont calculées automatiquement après validation</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
