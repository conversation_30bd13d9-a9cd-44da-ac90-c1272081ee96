   complet:
    {% extends 'base.html.twig' %}

{% block title %}Dashboard - OSI Manager{% endblock %}

{% block body %}
<div class="px-4 py-6 sm:px-0">
    <!-- En-tête avec dégradé -->
    <div class="mb-8 relative overflow-hidden bg-gradient-to-r from-blue-600 via-purple-600 to-indigo-600 rounded-2xl p-8 text-white shadow-xl">
        <div class="absolute inset-0 bg-black opacity-10"></div>
        <div class="relative z-10">
            <h1 class="text-4xl font-bold mb-2 bg-gradient-to-r from-white to-blue-100 bg-clip-text text-transparent">
                Dashboard
            </h1>
            <p class="text-blue-100 text-lg">Vue d'ensemble des missions et utilisateurs</p>
            <div class="mt-4 flex items-center space-x-4">
                <div class="flex items-center space-x-2">
                    <div class="w-3 h-3 bg-green-400 rounded-full animate-pulse"></div>
                    <span class="text-sm text-blue-100">Système opérationnel</span>
                </div>
                <div class="text-sm text-blue-100">
                    {{ "now"|date("d/m/Y à H:i") }}
                </div>
            </div>
        </div>
        <!-- Motif décoratif -->
        <div class="absolute top-0 right-0 -mt-4 -mr-4 w-32 h-32 bg-white opacity-5 rounded-full"></div>
        <div class="absolute bottom-0 left-0 -mb-8 -ml-8 w-24 h-24 bg-white opacity-5 rounded-full"></div>
    </div>

    <!-- Statistiques générales avec design moderne -->
    {% if not app.user.isUser() %}
        <div class="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-4 mb-8">
    {% else %}
        <div class="flex justify-center gap-6 mb-8 mx-auto max-w-4xl">
    {% endif %}
        <!-- Total Utilisateurs -->
        {% if not app.user.isUser() %}
            <div class="group relative bg-gradient-to-br from-blue-50 to-indigo-100 overflow-hidden shadow-lg rounded-2xl border border-blue-200 hover:shadow-xl transition-all duration-300 hover:-translate-y-1">
                <div class="absolute inset-0 bg-gradient-to-br from-blue-500/10 to-indigo-500/10"></div>
                <div class="relative p-6">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center space-x-4">
                            <div class="w-12 h-12 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-xl flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform duration-300">
                                <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
                                </svg>
                            </div>
                            <div>
                                <p class="text-sm font-medium text-blue-600 mb-1">Utilisateurs</p>
                                <p class="text-2xl font-bold text-gray-900">{{ statsCollaborateurs.total }}</p>
                            </div>
                        </div>
                        <div class="text-right">
                            <div class="flex items-center text-green-600 text-sm font-medium">
                                <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
                                </svg>
                                Actif
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Forfait Jour -->
            <div class="group relative bg-gradient-to-br from-green-50 to-emerald-100 overflow-hidden shadow-lg rounded-2xl border border-green-200 hover:shadow-xl transition-all duration-300 hover:-translate-y-1">
                <div class="absolute inset-0 bg-gradient-to-br from-green-500/10 to-emerald-500/10"></div>
                <div class="relative p-6">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center space-x-4">
                            <div class="w-12 h-12 bg-gradient-to-br from-green-500 to-emerald-600 rounded-xl flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform duration-300">
                                <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                                </svg>
                            </div>
                            <div>
                                <p class="text-sm font-medium text-green-600 mb-1">Forfait Jour</p>
                                <p class="text-2xl font-bold text-gray-900">{{ statsCollaborateurs.forfaitJour }}</p>
                            </div>
                        </div>
                        <div class="text-right">
                            <div class="flex items-center text-blue-600 text-sm font-medium">
                                <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                                Configuré
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        {% endif %}

        <!-- Total Missions -->
        <div class="group relative bg-gradient-to-br from-purple-50 to-violet-100 overflow-hidden shadow-lg rounded-2xl border border-purple-200 hover:shadow-xl transition-all duration-300 hover:-translate-y-1">
            <div class="absolute inset-0 bg-gradient-to-br from-purple-500/10 to-violet-500/10"></div>
            <div class="relative p-6">
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-4">
                        <div class="w-12 h-12 bg-gradient-to-br from-purple-500 to-violet-600 rounded-xl flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform duration-300">
                            <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3.055 11H5a2 2 0 012 2v1a2 2 0 002 2 2 2 0 012 2v2.945M8 3.935V5.5A2.5 2.5 0 0010.5 8h.5a2 2 0 012 2 2 2 0 104 0 2 2 0 012-2h1.064M15 20.488V18a2 2 0 012-2h3.064M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                        </div>
                        <div>
                            <p class="text-sm font-medium text-purple-600 mb-1">Total Missions</p>
                            <p class="text-2xl font-bold text-gray-900">{{ statsMissions.total }}</p>
                        </div>
                    </div>
                    <div class="text-right">
                        <div class="flex items-center text-purple-600 text-sm font-medium">
                            <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                            </svg>
                            Global
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Missions en cours -->
        <div class="group relative bg-gradient-to-br from-orange-50 to-amber-100 overflow-hidden shadow-lg rounded-2xl border border-orange-200 hover:shadow-xl transition-all duration-300 hover:-translate-y-1">
            <div class="absolute inset-0 bg-gradient-to-br from-orange-500/10 to-amber-500/10"></div>
            <div class="relative p-6">
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-4">
                        <div class="w-12 h-12 bg-gradient-to-br from-orange-500 to-amber-600 rounded-xl flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform duration-300">
                            <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                            </svg>
                        </div>
                        <div>
                            <p class="text-sm font-medium text-orange-600 mb-1">En cours</p>
                            <p class="text-2xl font-bold text-gray-900">{{ missionsEnCours|length }}</p>
                        </div>
                    </div>
                    <div class="text-right">
                        <div class="flex items-center text-orange-600 text-sm font-medium">
                            <div class="w-2 h-2 bg-orange-500 rounded-full animate-pulse mr-2"></div>
                            Actives
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Contenu principal avec design moderne -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
        <!-- Missions en cours -->
        <div class="bg-white shadow-xl rounded-2xl border border-gray-100 overflow-hidden">
            <div class="bg-gradient-to-r from-blue-500 to-purple-600 px-6 py-4">
                <h3 class="text-xl font-bold text-white flex items-center">
                    <svg class="w-6 h-6 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                    </svg>
                    Missions en cours
                </h3>
            </div>
            <div class="p-6">
                {% if missionsEnCours|length > 0 %}
                    <div class="space-y-4">
                        {% for mission in missionsEnCours %}
                            <div class="group relative bg-gradient-to-r from-gray-50 to-blue-50 border border-gray-200 rounded-xl p-4 hover:shadow-lg transition-all duration-300 hover:border-blue-300">
                                <div class="flex items-start justify-between">
                                    <div class="flex-1">
                                        <div class="flex items-center space-x-3 mb-2">
                                            <div class="w-3 h-3 bg-green-500 rounded-full animate-pulse"></div>
                                            <h4 class="text-lg font-semibold text-gray-900 group-hover:text-blue-700 transition-colors">
                                                {{ mission.titre }}
                                            </h4>
                                        </div>
                                        <div class="flex items-center space-x-4 text-sm text-gray-600 mb-3">
                                            <div class="flex items-center space-x-1">
                                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                                </svg>
                                                <span>{{ mission.pays }}</span>
                                            </div>
                                            {% if mission.users|length > 0 %}
                                                <div class="flex items-center space-x-1">
                                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                                                    </svg>
                                                    <span>
                                                        {% for user in mission.users %}
                                                            {{ user.nomComplet }}{% if not loop.last %}, {% endif %}
                                                        {% endfor %}
                                                    </span>
                                                </div>
                                            {% endif %}
                                        </div>
                                    </div>
                                    <div class="text-right space-y-2">
                                        <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium shadow-sm
                                            {% if mission.zone == 'EURO' %}bg-gradient-to-r from-blue-100 to-blue-200 text-blue-800 border border-blue-300{% else %}bg-gradient-to-r from-green-100 to-green-200 text-green-800 border border-green-300{% endif %}">
                                            {{ mission.zone == 'EURO' ? 'Zone Euro' : 'Hors Zone Euro' }}
                                        </span>
                                        <div class="flex items-center justify-end space-x-1 text-xs text-gray-500">
                                            <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                                            </svg>
                                            <span>Niveau {{ mission.niveau }}</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        {% endfor %}
                    </div>
                {% else %}
                    <div class="text-center py-12">
                        <svg class="w-16 h-16 text-gray-300 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
                        </svg>
                        <p class="text-gray-500 text-lg">Aucune mission en cours</p>
                        <p class="text-gray-400 text-sm mt-1">Les missions actives apparaîtront ici</p>
                    </div>
                {% endif %}
                <div class="mt-6 pt-4 border-t border-gray-100">
                    <a href="{{ path('app_missions') }}" class="inline-flex items-center text-sm font-medium text-blue-600 hover:text-blue-700 group transition-colors">
                        <span>Voir toutes les missions</span>
                        <svg class="w-4 h-4 ml-2 group-hover:translate-x-1 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"></path>
                        </svg>
                    </a>
                </div>
            </div>
        </div>

        <!-- Prochaines missions -->
        <div class="bg-white shadow-xl rounded-2xl border border-gray-100 overflow-hidden">
            <div class="bg-gradient-to-r from-green-500 to-teal-600 px-6 py-4">
                <h3 class="text-xl font-bold text-white flex items-center">
                    <svg class="w-6 h-6 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                    </svg>
                    Prochaines missions
                </h3>
            </div>
            <div class="p-6">
                {% if prochaines|length > 0 %}
                    <div class="space-y-4">
                        {% for mission in prochaines %}
                            <div class="group relative bg-gradient-to-r from-gray-50 to-green-50 border border-gray-200 rounded-xl p-4 hover:shadow-lg transition-all duration-300 hover:border-green-300">
                                <div class="flex items-start justify-between">
                                    <div class="flex-1">
                                        <div class="flex items-center space-x-3 mb-2">
                                            <div class="w-3 h-3 bg-blue-500 rounded-full"></div>
                                            <h4 class="text-lg font-semibold text-gray-900 group-hover:text-green-700 transition-colors">
                                                {{ mission.titre }}
                                            </h4>
                                        </div>
                                        <div class="flex items-center space-x-4 text-sm text-gray-600 mb-3">
                                            <div class="flex items-center space-x-1">
                                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                                </svg>
                                                <span>{{ mission.pays }}</span>
                                            </div>
                                            {% if mission.users|length > 0 %}
                                                <div class="flex items-center space-x-1">
                                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                                                    </svg>
                                                    <span>
                                                        {% for user in mission.users %}
                                                            {{ user.nomComplet }}{% if not loop.last %}, {% endif %}
                                                        {% endfor %}
                                                    </span>
                                                </div>
                                            {% else %}
                                                <div class="flex items-center space-x-1 text-orange-600">
                                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                                                    </svg>
                                                    <span>Aucun utilisateur assigné</span>
                                                </div>
                                            {% endif %}
                                        </div>
                                    </div>
                                    <div class="text-right space-y-2">
                                        <div class="bg-gradient-to-r from-blue-100 to-blue-200 text-blue-800 px-3 py-1 rounded-full text-sm font-medium border border-blue-300">
                                            {{ mission.dateDebut|date('d/m/Y') }}
                                        </div>
                                        <div class="flex items-center justify-end space-x-1 text-xs text-gray-500">
                                            <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                            </svg>
                                            <span>{{ mission.dureeJours }} jour(s)</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        {% endfor %}
                    </div>
                {% else %}
                    <div class="text-center py-12">
                        <svg class="w-16 h-16 text-gray-300 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                        </svg>
                        <p class="text-gray-500 text-lg">Aucune mission prochaine</p>
                        <p class="text-gray-400 text-sm mt-1">Les missions planifiées apparaîtront ici</p>
                    </div>
                {% endif %}
                <div class="mt-6 pt-4 border-t border-gray-100">
                    <a href="{{ path('app_calendrier') }}" class="inline-flex items-center text-sm font-medium text-green-600 hover:text-green-700 group transition-colors">
                        <span>Voir le calendrier</span>
                        <svg class="w-4 h-4 ml-2 group-hover:translate-x-1 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"></path>
                        </svg>
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Actions rapides avec design moderne -->
    <div class="mt-8 bg-white shadow-xl rounded-2xl border border-gray-100 overflow-hidden">
        <div class="bg-gradient-to-r from-indigo-500 to-purple-600 px-6 py-4">
            <h3 class="text-xl font-bold text-white flex items-center">
                <svg class="w-6 h-6 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                </svg>
                Actions rapides
            </h3>
        </div>
        <div class="p-6">
            <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
                <a href="{{ path('app_calendrier') }}" class="group relative bg-gradient-to-br from-blue-50 to-indigo-100 p-6 rounded-2xl border border-blue-200 hover:shadow-xl transition-all duration-300 hover:-translate-y-2 hover:border-blue-300">
                    <div class="absolute inset-0 bg-gradient-to-br from-blue-500/10 to-indigo-500/10 rounded-2xl"></div>
                    <div class="relative">
                        <div class="w-12 h-12 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-xl flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300 shadow-lg">
                            <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                            </svg>
                        </div>
                        <h4 class="text-lg font-bold text-blue-900 mb-2 group-hover:text-blue-700 transition-colors">Calendrier</h4>
                        <p class="text-sm text-blue-700 group-hover:text-blue-600 transition-colors">Gérer les segments</p>
                    </div>
                </a>

                <a href="{{ path('app_users') }}" class="group relative bg-gradient-to-br from-green-50 to-emerald-100 p-6 rounded-2xl border border-green-200 hover:shadow-xl transition-all duration-300 hover:-translate-y-2 hover:border-green-300">
                    <div class="absolute inset-0 bg-gradient-to-br from-green-500/10 to-emerald-500/10 rounded-2xl"></div>
                    <div class="relative">
                        <div class="w-12 h-12 bg-gradient-to-br from-green-500 to-emerald-600 rounded-xl flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300 shadow-lg">
                            <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
                            </svg>
                        </div>
                        <h4 class="text-lg font-bold text-green-900 mb-2 group-hover:text-green-700 transition-colors">Utilisateurs</h4>
                        <p class="text-sm text-green-700 group-hover:text-green-600 transition-colors">Gérer l'équipe</p>
                    </div>
                </a>

                <a href="{{ path('app_heures') }}" class="group relative bg-gradient-to-br from-purple-50 to-violet-100 p-6 rounded-2xl border border-purple-200 hover:shadow-xl transition-all duration-300 hover:-translate-y-2 hover:border-purple-300">
                    <div class="absolute inset-0 bg-gradient-to-br from-purple-500/10 to-violet-500/10 rounded-2xl"></div>
                    <div class="relative">
                        <div class="w-12 h-12 bg-gradient-to-br from-purple-500 to-violet-600 rounded-xl flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300 shadow-lg">
                            <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                        </div>
                        <h4 class="text-lg font-bold text-purple-900 mb-2 group-hover:text-purple-700 transition-colors">Heures</h4>
                        <p class="text-sm text-purple-700 group-hover:text-purple-600 transition-colors">Suivi du temps</p>
                    </div>
                </a>

                <a href="{{ path('app_primes') }}" class="group relative bg-gradient-to-br from-orange-50 to-amber-100 p-6 rounded-2xl border border-orange-200 hover:shadow-xl transition-all duration-300 hover:-translate-y-2 hover:border-orange-300">
                    <div class="absolute inset-0 bg-gradient-to-br from-orange-500/10 to-amber-500/10 rounded-2xl"></div>
                    <div class="relative">
                        <div class="w-12 h-12 bg-gradient-to-br from-orange-500 to-amber-600 rounded-xl flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300 shadow-lg">
                            <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                            </svg>
                        </div>
                        <h4 class="text-lg font-bold text-orange-900 mb-2 group-hover:text-orange-700 transition-colors">Primes</h4>
                        <p class="text-sm text-orange-700 group-hover:text-orange-600 transition-colors">Calcul des primes</p>
                    </div>
                </a>
            </div>
        </div>
    </div>


    <!-- Utilisateurs actifs avec design moderne -->
    {% if not app.user.isUser() %}
        {% if usersActifs|length > 0 %}
            <div class="mt-8 bg-white shadow-xl rounded-2xl border border-gray-100 overflow-hidden">
                <div class="bg-gradient-to-r from-teal-500 to-cyan-600 px-6 py-4">
                    <h3 class="text-xl font-bold text-white flex items-center">
                        <svg class="w-6 h-6 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                        </svg>
                        Utilisateurs en mission
                    </h3>
                </div>
                <div class="p-6">
                    <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
                        {% for user in usersActifs %}
                            <div class="group relative bg-gradient-to-br from-gray-50 to-teal-50 border border-gray-200 rounded-xl p-4 hover:shadow-lg transition-all duration-300 hover:border-teal-300">
                                <div class="flex items-center space-x-4">
                                    <div class="relative flex-shrink-0">
                                        <div class="w-12 h-12 bg-gradient-to-br from-teal-500 to-cyan-600 rounded-full flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform duration-300">
                                            <span class="text-white font-bold text-sm">{{ user.prenom|first }}{{ user.nom|first }}</span>
                                        </div>
                                        <!-- Indicateur de statut en ligne -->
                                        <div class="absolute -bottom-1 -right-1 w-4 h-4 bg-green-500 border-2 border-white rounded-full animate-pulse"></div>
                                    </div>
                                    <div class="flex-1 min-w-0">
                                        <h4 class="text-sm font-bold text-gray-900 group-hover:text-teal-700 transition-colors truncate">
                                            {{ user.nomComplet }}
                                        </h4>
                                        <div class="flex items-center space-x-1 mt-1">
                                            <svg class="w-3 h-3 text-teal-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 13.255A23.931 23.931 0 0112 15c-3.183 0-6.22-.62-9-1.745M16 6V4a2 2 0 00-2-2h-4a2 2 0 00-2-2v2m8 0V6a2 2 0 012 2v6a2 2 0 01-2 2H8a2 2 0 01-2-2V8a2 2 0 012-2z"></path>
                                            </svg>
                                            <p class="text-xs text-gray-600 truncate">{{ user.roleDisplay }}</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
        {% endif %}
    {% endif %}

</div>
{% endblock %}
